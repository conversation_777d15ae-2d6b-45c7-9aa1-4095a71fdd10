{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "lib": ["ES2022", "DOM"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "noEmit": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "removeComments": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": true, "noUncheckedIndexedAccess": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/parser/*": ["src/parser/*"], "@/transformer/*": ["src/transformer/*"], "@/generator/*": ["src/generator/*"], "@/runtime/*": ["src/runtime/*"], "@/compiler/*": ["src/compiler/*"], "@/cli/*": ["src/cli/*"], "@/utils/*": ["src/utils/*"], "@/types/*": ["src/types/*"]}}, "include": ["src/**/*", "tests/**/*", "examples/**/*"], "exclude": ["node_modules", "dist", "**/*.js"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}